import { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>ack,
  Typography,
  Grid,
  Card,
  CardContent,
  Checkbox,
  FormControlLabel,
} from '@mui/material';
import { useFormContext } from 'react-hook-form';
import { Iconify } from 'src/components/iconify';
import { useToolsApi, ToolsQueryParams } from 'src/services/api/use-tools-api';
import { useDebounce } from 'src/hooks/use-debounce';
import { AgentFormValues } from '../config/agent-form-config';
import ServiceSearchBar from '../components/service-search-bar';

// ----------------------------------------------------------------------

interface ToolsStepProps {
  isEditing: boolean;
}

export function ToolsStep({ isEditing }: ToolsStepProps) {
  const { setValue, watch } = useFormContext<AgentFormValues>();
  const [searchQuery, setSearchQuery] = useState('');
  const [currentQuery, setCurrentQuery] = useState<string | null>(null);

  // Debounce search query
  const debouncedSearchQuery = useDebounce(searchQuery, 1000);

  // Watch current selection
  const selectedTools = watch('toolsId') || [];

  // Update current query when debounced search changes
  useEffect(() => {
    const trimmedQuery = debouncedSearchQuery.trim();
    setCurrentQuery(trimmedQuery === '' ? null : trimmedQuery);
  }, [debouncedSearchQuery]);

  // Build query parameters for API
  const queryParams: ToolsQueryParams = {
    take: 15,
    skip: 0,
    ...(currentQuery && { name: currentQuery }),
  };

  // Get tools from API with search parameters
  const { useGetTools } = useToolsApi();
  const { data: toolsResponse, isLoading } = useGetTools(queryParams);
  const tools = toolsResponse?.tools || [];

  // Handle tool selection
  const handleToolToggle = (toolId: number) => {
    if (isEditing) return; // Prevent toggling if in editing mode

    const currentTools = selectedTools || [];
    const newSelection = currentTools.includes(toolId)
      ? currentTools.filter((id) => id !== toolId)
      : [...currentTools, toolId];

    setValue('toolsId', newSelection, { shouldValidate: true });
  };

  // Handle search input change
  const handleSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  }, []);

  return (
    <>
      <Typography color="rgba(15, 14, 17, 0.65)" variant="h5" sx={{ mb: '20px' }}>
        Add tools to your agents template
      </Typography>
      <Stack spacing={2} bgcolor="white" p="20px" borderRadius="10px">
        <ServiceSearchBar
          query={searchQuery}
          onChange={handleSearchChange}
          placeholder="Search tools..."
        />
        <Grid container spacing={2}>
          {tools.map((tool) => {
            const isSelected = selectedTools.includes(tool.id);
            return (
              <Grid item xs={12} key={tool.id}>
                <Card
                  variant="outlined"
                  sx={{
                    cursor: isEditing ? 'default' : 'pointer',
                    bgcolor: 'divider',
                  }}
                  onClick={() => handleToolToggle(tool.id)} // Click handler
                >
                  <CardContent>
                    <FormControlLabel
                      control={<Checkbox checked={isSelected} disabled={isEditing} />}
                      label={
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Iconify icon={tool.icon} />
                          <Typography variant="subtitle2">{tool.name}</Typography>
                        </Stack>
                      }
                      sx={{ width: '100%' }}
                    />
                  </CardContent>
                </Card>
              </Grid>
            );
          })}
        </Grid>
      </Stack>
    </>
  );
}

export default ToolsStep;